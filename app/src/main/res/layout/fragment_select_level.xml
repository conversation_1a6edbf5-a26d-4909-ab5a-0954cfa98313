<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/secondaryBackground"
    android:fillViewport="true"
    tools:context=".views.fragments.FragmentSelectLevel">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/instr_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_black"
            android:text="Select your"
            android:textColor="@color/black"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/instr_text_b"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/poppins_black"
            android:text="experience level"
            android:textColor="@color/md_green_500"
            android:textSize="36sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/instr_text" />

        <ImageButton
            android:id="@+id/btn_beginner_default"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="100dp"
            android:background="?selectableItemBackgroundBorderless"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/instr_text_b"
            app:srcCompat="@drawable/beginner_default" />

        <FrameLayout
            android:id="@+id/intermediate_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            app:layout_constraintEnd_toStartOf="@+id/professional_container"
            app:layout_constraintStart_toStartOf="@+id/instr_text_b"
            app:layout_constraintTop_toBottomOf="@+id/btn_beginner_default">

            <ImageButton
                android:id="@+id/btn_intermediate_default"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?selectableItemBackgroundBorderless"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/intermediate_default" />

            <ImageView
                android:id="@+id/intermediate_lock_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/locked_overlay"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/professional_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/intermediate_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/intermediate_container"
            app:layout_constraintTop_toTopOf="@+id/intermediate_container">

            <ImageButton
                android:id="@+id/btn_professional_default"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?selectableItemBackgroundBorderless"
                android:scaleType="centerCrop"
                app:srcCompat="@drawable/professional_default" />

            <ImageView
                android:id="@+id/professional_lock_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/locked_overlay"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/tv_selected_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/primaryDarkColor"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/btn_proceed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/intermediate_container"
            tools:text="Selected Level: " />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_proceed"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:fontFamily="@font/poppins_bold"
            android:text="Proceed"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>